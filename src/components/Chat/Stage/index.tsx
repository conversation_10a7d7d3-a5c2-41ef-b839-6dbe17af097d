import styled from '@emotion/styled';
import {ErrorBoundary} from 'react-error-boundary';
import {useEffect} from 'react';
import {useCurrentChat, useStage} from '@/regions/staff/chat';
import {colors} from '@/constants/colors';
import {apiPostReport} from '@/api/staff';
import {MessageProvider} from '../Provider/MessageProvider';
import {AlertFallbackPage} from './AlertFallbackPage';
import {StageActions} from './StageActions';
import {StageIframePanel} from './StageIframePanel';
import {StageStepPanel} from './StageStepPanel';
import {StageDiffPanel} from './StageDiffPanel';
import {StageTextPanel} from './StageTextPanel';
import {StageTabPanel} from './StageTabPanel';
import {StageF2CPanel} from './StageF2CPanel/index';
import {StageCollapsePanel} from './StageCollapsePanel';
import {StageIapiPanel} from './StageIapiPanel';

const StageContent = () => {
    const {currentStageId, conversationId, agentId, currentTaskId} = useCurrentChat();
    const stage = useStage(currentTaskId, currentStageId);

    useEffect(
        () => {
            if (currentStageId === '结果预览') {
                apiPostReport({
                    agentId,
                    metricName: 'taskResultView',
                    metricValue: 1,
                    labels: [
                        {
                            key: 'conversationId',
                            value: conversationId || '',
                        },
                    ],
                });
            }
        },
        [agentId, currentStageId, conversationId]
    );

    switch (stage.type) {
        case 'text':
            return <StageTextPanel stage={stage} />;
        case 'iframe':
            return <StageIframePanel stage={stage} />;
        case 'steps':
            return <StageStepPanel stage={stage} />;
        case 'diff':
            return <StageDiffPanel stage={stage} />;
        case 'f2c':
            return <StageF2CPanel stage={stage} />;
        case 'tabs':
            return <StageTabPanel stage={stage} />;
        case 'collapses':
            return <StageCollapsePanel stage={stage} />;
        case 'iapi':
            // 这个组件的useEffect中依赖了disable了exhaustive-deps，需要使用key强制刷新
            return <StageIapiPanel stage={stage} key={stage.taskId + stage.id} />;
        default:
            return null;
    }
};

const Split = styled.div`
    height: 1px;
    background-color: ${colors['gray-5']};
`;

export const Stage = () => {
    const {agentId, currentTaskId} = useCurrentChat();
    const stage = useStage(currentTaskId, '结果预览');
    console.log(stage, 'stage');
    if (!stage) {
        return null;
    }
    const {actions, messageId} = stage;
    return (
        <MessageProvider messageId={messageId} agentId={agentId}>
            <ErrorBoundary
                key={currentTaskId}
                FallbackComponent={AlertFallbackPage}
            >
                <StageContent />
            </ErrorBoundary>
            {Boolean(actions?.length) && (
                <>
                    <Split />
                    <StageActions actions={actions} />
                </>
            )}
        </MessageProvider>
    );
};
