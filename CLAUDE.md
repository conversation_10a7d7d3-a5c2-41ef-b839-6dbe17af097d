# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是 Comate Stack 的前端项目，一个基于 React + TypeScript + Vite 的大型 DevOps 平台。项目采用模块化路由架构，包含多个子应用：icode、iplayground、ievalue、mcp、staff、comatestack。

## 常用开发命令

### 基础开发
```bash
yarn                # 安装依赖
yarn start          # 启动开发服务器 (http://localhost:3000)
```

### 代码质量检查
```bash
yarn lint           # ESLint 代码检查
yarn lint-strict    # 严格的代码检查
yarn lint-type      # TypeScript 类型检查
yarn lint-less      # Less 样式检查
```

### 构建和测试
```bash
yarn build          # 生产环境构建
yarn test           # 运行测试
```

### 图标生成
```bash
yarn generate-icons     # 生成通用图标
yarn generate-mcp-icons # 生成 MCP 相关图标
```

## 项目架构

### 路由架构
- **入口文件**: `src/index.tsx` - 根据 APP_NAME 动态加载不同的子应用路由
- **子应用模块**:
  - `icode`: 代码管理和评审
  - `ievalue`: AI 评测平台
  - `iplayground`: 流程编排工具
  - `mcp`: MCP 协议支持
  - `staff`: 数字员工
  - `comatestack`: 主平台功能

### 目录结构
```
src/
├── api/           # API 接口定义，按模块组织
├── assets/        # 静态资源（图标、图片等）
├── components/    # 可复用组件库
├── constants/     # 常量定义，按模块分类
├── design/        # 设计系统组件
├── hooks/         # 自定义 React Hooks
├── regions/       # 状态管理（region-react）
├── routers/       # 路由配置，按子应用组织
├── styles/        # 样式文件
├── utils/         # 工具函数
└── [module]/      # 各子应用的页面组件
```

### 技术栈特点
- **路径别名**: `@/*` 指向 `src/*`
- **状态管理**: 使用 `region-react` 库
- **样式方案**: Emotion + Ant Design
- **图标系统**: 自定义 SVG 图标生成流程
- **模块联邦**: 支持微前端架构

## 开发约定

### 代码规范
- 使用严格的 TypeScript 配置
- ESLint + Prettier 代码格式化
- Git 提交前会自动运行代码检查（husky hooks）
- 提交格式：包含任务卡片和标题

### React Hooks 注意事项
- Hook 必须在 React 函数组件或自定义 Hook 中调用
- 不能在循环、条件语句或回调函数中调用 Hook
- 项目中使用了 `eslint-disable react-hooks/rules-of-hooks` 的地方需特别注意

### 组件开发
- 优先查看现有组件，遵循已有的代码风格和架构模式
- 新组件应放在合适的模块目录下
- 复用性强的组件放在 `src/components/` 下

### API 开发
- API 接口按模块组织在 `src/api/` 下
- 使用 TypeScript 进行类型定义
- 遵循项目的 API 调用规范

## 环境配置

### 本地开发
- Node.js 版本: 20.19.4 (通过 volta 管理)
- Yarn 版本: v1 (必须使用 v1)
- 本地开发连接沙盒后端环境

### 部署流程
1. **联调环境**: 提交到 `refs/for/master` 自动部署
2. **沙盒环境**: 合入 `master` 后自动部署
3. **灰度环境**: `master` 合入后自动编译和上线
4. **生产环境**: 创建 release 分支手动上线

### 分支管理
- 主分支: `master`
- 发布分支: `release-YYYYMMDD` 格式
- hotfix 正常提交到 `master`